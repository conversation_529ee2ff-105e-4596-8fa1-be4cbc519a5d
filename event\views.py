from rest_framework import generics, permissions, status, viewsets, filters
from rest_framework.response import Response
from django.db.models import Q
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample
from .models import Event, TicketClass, Contestant, Poll, PollOption, Vote, EventFile
from .serializers import (
    EventSerializer, TicketClassSerializer, ContestantSerializer,
    PollSerializer, PollOptionSerializer, VoteSerializer, PopularEventSerializer,
    EventFileSerializer
)
from .permissions import IsEventHostOrReadOnly
from account.permissions import AllowAnyAccess
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.permissions import IsAuthenticated
from .utils.popularity import get_trending_events
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework import status
from django.shortcuts import get_object_or_404
from account.models import CustomUser
from payment.models import Payment  # Assuming you have a Payment model for earnings
from django.db import models
from .models import Ticket


class EventListCreateView(generics.ListCreateAPIView):
    serializer_class = EventSerializer
    permission_classes = [AllowAnyAccess]
    pagination_class = PageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'description', 'location']
    ordering_fields = ['start_date', 'end_date', 'created_at']
    parser_classes = (MultiPartParser, FormParser)
    
    def get_queryset(self):
        queryset = Event.objects.all()
        
        # Filter by event_type
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        # Other filters
        location = self.request.query_params.get('location')
        category = self.request.query_params.get('category')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        
        if location:
            queryset = queryset.filter(location__icontains=location)
        if category:
            queryset = queryset.filter(category=category)
        if start_date and end_date:
            queryset = queryset.filter(start_date__gte=start_date, end_date__lte=end_date)
        if min_price and max_price:
            queryset = queryset.filter(ticket_classes__price__gte=min_price, ticket_classes__price__lte=max_price)
        
        return queryset
    
    def perform_create(self, serializer):
        # If user is authenticated, use their account, otherwise create without host
        if self.request.user.is_authenticated:
            serializer.save(host=self.request.user)
        else:
            serializer.save()
            
    @extend_schema(
        parameters=[
            OpenApiParameter(
                name="event_type",
                description="Filter events by type (event, contest, form)",
                required=False,
                type=str,
                enum=["event", "contest", "form"]
            ),
            OpenApiParameter(
                name="location",
                description="Filter events by location",
                required=False,
                type=str
            ),
            OpenApiParameter(
                name="category",
                description="Filter events by category",
                required=False,
                type=str
            ),
            OpenApiParameter(
                name="start_date",
                description="Filter events by start date (YYYY-MM-DD)",
                required=False,
                type=str
            ),
            OpenApiParameter(
                name="end_date",
                description="Filter events by end date (YYYY-MM-DD)",
                required=False,
                type=str
            ),
            OpenApiParameter(
                name="min_price",
                description="Filter events by minimum ticket price",
                required=False,
                type=float
            ),
            OpenApiParameter(
                name="max_price",
                description="Filter events by maximum ticket price",
                required=False,
                type=float
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class EventDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Event.objects.all()
    serializer_class = EventSerializer
    permission_classes = [IsEventHostOrReadOnly]
    
    def retrieve(self, request, *args, **kwargs):
        # Get the event
        instance = self.get_object()
        
        # Increment view count
        instance.increment_view()
        
        # Serialize and return
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
        
    @extend_schema(
        summary="Update an event",
        description="Update an existing event. Only the host of the event can perform this action."
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)
        
    @extend_schema(
        summary="Delete an event",
        description="Delete an existing event. Only the host of the event can perform this action."
    )
    def delete(self, request, *args, **kwargs):
        return super().delete(request, *args, **kwargs)


class TicketClassListCreateView(generics.ListCreateAPIView):
    serializer_class = TicketClassSerializer
    permission_classes = [AllowAnyAccess]
    
    def get_queryset(self):
        return TicketClass.objects.all()
    
    def perform_create(self, serializer):
        serializer.save()


class TicketClassDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = TicketClass.objects.all()
    serializer_class = TicketClassSerializer
    permission_classes = [IsEventHostOrReadOnly]


class ContestantListCreateView(generics.ListCreateAPIView):
    serializer_class = ContestantSerializer
    permission_classes = [AllowAnyAccess]
    
    def get_queryset(self):
        return Contestant.objects.all()
    
    def perform_create(self, serializer):
        serializer.save()


class ContestantDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Contestant.objects.all()
    serializer_class = ContestantSerializer
    permission_classes = [IsEventHostOrReadOnly]


class PollViewSet(viewsets.ModelViewSet):
    queryset = Poll.objects.all()
    serializer_class = PollSerializer
    permission_classes = [IsEventHostOrReadOnly]


class PollOptionViewSet(viewsets.ModelViewSet):
    queryset = PollOption.objects.all()
    serializer_class = PollOptionSerializer
    permission_classes = [IsEventHostOrReadOnly]


class VoteViewSet(viewsets.ModelViewSet):
    queryset = Vote.objects.all()
    serializer_class = VoteSerializer
    permission_classes = [AllowAnyAccess]


class TicketClassViewSet(viewsets.ModelViewSet):
    queryset = TicketClass.objects.all()
    serializer_class = TicketClassSerializer
    permission_classes = [IsEventHostOrReadOnly]


class PopularEventsView(generics.ListAPIView):
    """View to get popular events"""
    serializer_class = PopularEventSerializer
    permission_classes = [AllowAnyAccess]
    
    def get_queryset(self):
        # Get limit from query params or default to 10
        limit = int(self.request.query_params.get('limit', 10))
        
        # Get popular events using our custom manager
        return Event.objects.get_popular(limit=limit)


class PopularEventsByMonthView(generics.ListAPIView):
    """View to get popular events for a specific month"""
    serializer_class = PopularEventSerializer
    permission_classes = [AllowAnyAccess]
    
    def get_queryset(self):
        # Get year and month from query params or default to current
        year = self.request.query_params.get('year')
        month = self.request.query_params.get('month')
        
        if year:
            year = int(year)
        if month:
            month = int(month)
            
        # Get limit from query params or default to 10
        limit = int(self.request.query_params.get('limit', 10))
        
        # Get popular events for the month
        return Event.objects.get_popular_by_month(
            year=year, 
            month=month, 
            limit=limit
        )


@api_view(['GET'])
def trending_events(request):
    """View to get trending events based on recent activity"""
    # Get limit from query params or default to 10
    limit = int(request.query_params.get('limit', 10))
    
    # Get trending events
    events = get_trending_events(limit=limit)
    
    # Serialize events
    serializer = PopularEventSerializer(events, many=True)
    
    # Return response
    return Response(serializer.data)


@api_view(['POST'])
def like_event(request, pk):
    """Endpoint to like an event"""
    try:
        event = Event.objects.get(pk=pk)
        event.increment_like()
        return Response({'status': 'success', 'like_count': event.like_count})
    except Event.DoesNotExist:
        return Response({'status': 'error', 'message': 'Event not found'}, status=404)


@api_view(['POST'])
def share_event(request, pk):
    """Endpoint to share an event"""
    try:
        event = Event.objects.get(pk=pk)
        event.increment_share()
        return Response({'status': 'success', 'share_count': event.share_count})
    except Event.DoesNotExist:
        return Response({'status': 'error', 'message': 'Event not found'}, status=404)


class EventFileUploadView(generics.CreateAPIView):
    queryset = EventFile.objects.all()
    serializer_class = EventFileSerializer
    parser_classes = (MultiPartParser, FormParser)
    permission_classes = [AllowAnyAccess]

    def perform_create(self, serializer):
        serializer.save()


class EventFileListView(generics.ListAPIView):
    serializer_class = EventFileSerializer
    permission_classes = [AllowAnyAccess]

    def get_queryset(self):
        event_id = self.kwargs['event_id']
        return EventFile.objects.filter(event_id=event_id)


@api_view(['GET'])
def event_stats(request, pk):
    event = get_object_or_404(Event, pk=pk)
    return Response({
        'view_count': event.view_count,
        'like_count': event.like_count,
        'share_count': event.share_count,
    })


@api_view(['GET'])
def event_ticket_metrics(request, pk):
    event = get_object_or_404(Event, pk=pk)
    total_sold = event.tickets.count()
    total_available = sum(tc.quantity_available for tc in event.ticket_classes.all())
    ticket_classes = [
        {
            'class_name': tc.class_name,
            'sold': event.tickets.filter(ticket_type=tc.class_name).count(),
            'available': tc.quantity_available,
            'price': tc.price,
        }
        for tc in event.ticket_classes.all()
    ]
    return Response({
        'total_sold': total_sold,
        'total_available': total_available,
        'ticket_classes': ticket_classes,
    })


@api_view(['POST'])
def increment_event_view(request, pk):
    event = get_object_or_404(Event, pk=pk)
    event.increment_view()
    return Response({'success': True, 'view_count': event.view_count}, status=status.HTTP_200_OK)

@api_view(['POST'])
def increment_event_like(request, pk):
    event = get_object_or_404(Event, pk=pk)
    event.increment_like()
    return Response({'success': True, 'like_count': event.like_count}, status=status.HTTP_200_OK)

@api_view(['POST'])
def increment_event_share(request, pk):
    event = get_object_or_404(Event, pk=pk)
    event.increment_share()
    return Response({'success': True, 'share_count': event.share_count}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_metrics(request):
    user = request.user
    # Forms created (assuming 'form' event_type)
    forms_created = Event.objects.filter(host=user, event_type='form').count()
    # Events hosted (not forms)
    events_hosted = Event.objects.filter(host=user).exclude(event_type='form').count()
    # Total earnings (sum of payments to user)
    total_earnings = Payment.objects.filter(user=user).aggregate(models.Sum('amount'))['amount__sum'] or 0
    # Polls created
    polls_created = Poll.objects.filter(event__host=user).count()
    # Tickets sold
    tickets_sold = Ticket.objects.filter(event__host=user).aggregate(models.Sum('quantity'))['quantity__sum'] or 0
    # Forms sold (assuming forms have tickets)
    forms_sold = Ticket.objects.filter(event__host=user, event__event_type='form').aggregate(models.Sum('quantity'))['quantity__sum'] or 0
    # Profile completion (simple example: percent of filled fields)
    profile_fields = [user.first_name, user.last_name, user.email, user.username]
    filled = sum(1 for f in profile_fields if f)
    profile_completion = int((filled / len(profile_fields)) * 100)
    return Response({
        'forms_created': forms_created,
        'events_hosted': events_hosted,
        'total_earnings': total_earnings,
        'polls_created': polls_created,
        'tickets_sold': tickets_sold,
        'forms_sold': forms_sold,
        'profile_completion': profile_completion,
    })
