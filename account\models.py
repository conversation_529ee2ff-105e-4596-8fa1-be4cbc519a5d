from django.contrib.auth.models import AbstractUser
from django.db import models
import uuid
from django.utils.translation import gettext_lazy as _

class CustomUser(AbstractUser):
    USER_TYPE_CHOICES = (
        ('host', 'Event Host'),
        ('participant', 'Event Participant'),
    )
    email = models.EmailField(_('email address'), unique=True)  # Make email required and unique
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='participant')
    unique_id = models.CharField(max_length=16, unique=True, blank=True)
    organization_name = models.CharField(max_length=255, blank=True, null=True)

    USERNAME_FIELD = 'email'  # Use email as the primary identifier
    REQUIRED_FIELDS = ['username']  # Username is still required by AbstractUser

    def save(self, *args, **kwargs):
        if not self.unique_id:
            self.unique_id = self.generate_unique_id()
        super().save(*args, **kwargs)

    def generate_unique_id(self):
        return uuid.uuid4().hex[:16]

    def __str__(self):
        return f"{self.email} ({self.user_type}) - {self.unique_id}"

class NewsletterSubscription(models.Model):
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=255, blank=True)
    subscribed_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    interests = models.JSONField(default=list, blank=True)
    last_email_sent = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return self.email
