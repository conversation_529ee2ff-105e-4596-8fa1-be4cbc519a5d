from django.core.management.base import BaseCommand
from django.utils import timezone
from account.models import NewsletterSubscription
import random

class Command(BaseCommand):
    help = 'Seeds the newsletter subscription table with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of newsletter subscriptions to create'
        )

    def handle(self, *args, **options):
        count = options['count']
        interests_options = [
            ['events'],
            ['contests'],
            ['events', 'contests'],
            ['events', 'workshops'],
            ['contests', 'workshops'],
            ['events', 'contests', 'workshops']
        ]
        
        companies = ['Tech', 'Corp', 'Industries', 'Solutions', 'Digital']
        domains = ['example.com', 'test.com', 'demo.com', 'sample.org', 'mock.net']
        
        self.stdout.write('Creating newsletter subscriptions...')
        
        for i in range(count):
            # Generate random email and name
            company = random.choice(companies)
            domain = random.choice(domains)
            email = f"subscriber{i+1}@{company.lower()}.{domain}"
            name = f"Subscriber {i+1}"
            
            # Create subscription with random interests
            subscription = NewsletterSubscription.objects.create(
                email=email,
                name=name,
                interests=random.choice(interests_options),
                is_active=random.choice([True, True, True, False]),  # 75% chance of being active
                last_email_sent=timezone.now() if random.random() > 0.5 else None
            )
            
            self.stdout.write(f'Created subscription for {email}')
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {count} newsletter subscriptions'))
