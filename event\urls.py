from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    EventListCreateView, EventDetailView,
    TicketClassListCreateView, TicketClassDetailView,
    ContestantListCreateView, ContestantDetailView,
    PollViewSet, PollOptionViewSet, VoteViewSet, TicketClassViewSet,
    # New views
    PopularEventsView, PopularEventsByMonthView, trending_events,
    like_event, share_event,
    EventFileUploadView, EventFileListView, event_stats, event_ticket_metrics,
    # Increment views
    increment_event_view, increment_event_like, increment_event_share,
    dashboard_metrics
)

router = DefaultRouter()
router.register(r'polls', PollViewSet, basename='poll')
router.register(r'poll-options', PollOptionViewSet, basename='poll-option')
router.register(r'votes', VoteViewSet, basename='vote')
router.register(r'ticket-classes', TicketClassViewSet, basename='ticket-class')

urlpatterns = [
    path('app/', include(router.urls)),
    path('events/', EventListCreateView.as_view(), name='event-list-create'),
    path('events/<int:pk>/', EventDetailView.as_view(), name='event-detail'),
    
    path('tickets/', TicketClassListCreateView.as_view(), name='ticket-list-create'),
    path('tickets/<int:pk>/', TicketClassDetailView.as_view(), name='ticket-detail'),

    path('contestants/', ContestantListCreateView.as_view(), name='contestant-list-create'),
    path('contestants/<int:pk>/', ContestantDetailView.as_view(), name='contestant-detail'),
    
    # Popular events endpoints
    path('events/popular/', PopularEventsView.as_view(), name='popular-events'),
    path('events/popular/month/', PopularEventsByMonthView.as_view(), name='popular-events-by-month'),
    path('events/trending/', trending_events, name='trending-events'),
    
    # Discover page endpoints
    path('discover/carousel/', PopularEventsByMonthView.as_view(), name='discover-carousel'),
    
    # Event engagement endpoints
    path('events/<int:pk>/like/', like_event, name='like-event'),
    path('events/<int:pk>/share/', share_event, name='share-event'),

    # File upload and list endpoints
    path('events/<int:event_id>/files/', EventFileListView.as_view(), name='event-file-list'),
    path('events/files/upload/', EventFileUploadView.as_view(), name='event-file-upload'),

    # Dynamic event stats
    path('events/<int:pk>/stats/', event_stats, name='event-stats'),

    # Consolidated ticket metrics
    path('events/<int:pk>/ticket-metrics/', event_ticket_metrics, name='event-ticket-metrics'),

    # Increment event engagement views
    path('events/<int:pk>/increment-view/', increment_event_view, name='event-increment-view'),
    path('events/<int:pk>/increment-like/', increment_event_like, name='event-increment-like'),
    path('events/<int:pk>/increment-share/', increment_event_share, name='event-increment-share'),

    # Dashboard metrics endpoint
    path('dashboard/metrics/', dashboard_metrics, name='dashboard-metrics'),
]
