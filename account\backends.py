from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()

class EmailBackend(ModelBackend):
    """
    Authenticate using email.
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # Check if email is provided directly or through username field
            email = kwargs.get('email') or username
            
            if email is None:
                print("DEBUG: No email provided")
                return None
                
            print(f"DEBUG: Attempting authentication for email: {email}")
            
            # Find user and verify password
            user = User.objects.get(email=email)
            if not user.is_active:
                print(f"DEBUG: User {email} is not active")
                return None
                
            if user.check_password(password):
                print(f"DEBUG: Authentication successful for {email}")
                return user
            else:
                print(f"DEBUG: Password verification failed for {email}")
                return None
                
        except User.DoesNotExist:
            print(f"DEBUG: No user found with email: {email}")
            User().set_password(password)  # Run password hasher once to prevent timing attacks
            return None
        except Exception as e:
            print(f"DEBUG: Unexpected error during authentication: {str(e)}")
            return None
        except User.DoesNotExist:
            print(f"DEBUG: No user found with email: {email}")
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user.
            User().set_password(password)
            return None