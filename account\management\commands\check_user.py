from django.core.management.base import BaseCommand
from account.models import CustomUser
from django.contrib.auth.hashers import check_password

class Command(BaseCommand):
    help = 'Check user credentials in the database'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str)
        parser.add_argument('password', type=str)

    def handle(self, *args, **options):
        email = options['email']
        password = options['password']

        try:
            user = CustomUser.objects.get(email=email)
            self.stdout.write(f"User found: {user.email}")
            self.stdout.write(f"Is active: {user.is_active}")
            self.stdout.write(f"Password check: {check_password(password, user.password)}")
        except CustomUser.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"No user found with email: {email}"))
