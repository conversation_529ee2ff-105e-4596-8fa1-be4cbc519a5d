import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'deebo.settings')
django.setup()

# Now you can import Django models
from django.db import connection

def fix_json_data():
    with connection.cursor() as cursor:
        # Get all JSON fields from the Event model
        json_fields = ['additional_features', 'voting_details', 'tags', 'organizer_contact', 'social_media_links']
        
        # Fix each JSON field
        for field in json_fields:
            # Set to NULL any field that doesn't look like valid JSON
            cursor.execute(f"""
                UPDATE event_event 
                SET {field} = NULL
                WHERE {field} IS NOT NULL 
                AND {field}::text NOT LIKE '{{%' 
                AND {field}::text NOT LIKE '[%';
            """)
        
        print("JSON fields fixed successfully!")

if __name__ == "__main__":
    fix_json_data()
