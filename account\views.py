from django.contrib.auth import authenticate, get_user_model
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from rest_framework.authentication import TokenAuthentication
from .serializers import RegisterSerializer, LoginSerializer, NewsletterSubscriptionSerializer
from django.shortcuts import get_object_or_404
from drf_spectacular.utils import extend_schema, OpenApiExample, OpenApiParameter
from .utils import format_error_response, format_validation_errors, format_success_response
from .models import CustomUser, NewsletterSubscription
from .permissions import AllowAnyAccess
import logging
import traceback

logger = logging.getLogger(__name__)

@extend_schema(
    request=RegisterSerializer,
    responses={201: RegisterSerializer},
    summary="Register a new user",
    description="Endpoint to register a new event host or participant. Provide necessary details like first name, last name, email, username, and password.",
)

class RegisterAPIView(APIView):
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                "success": True,
                "message": "User registered successfully",
                "data": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "user_type": user.user_type,
                    "unique_id": user.unique_id,
                    "organization_name": user.organization_name,
                }
            }, status=status.HTTP_201_CREATED)
        
        return Response(format_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)
    
# List all users
class UserListAPIView(APIView):
    permission_classes = [AllowAnyAccess]  # Ensure only authenticated users can access

    def get(self, request):
        users = CustomUser.objects.all()
        serializer = RegisterSerializer(users, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

# Retrieve user details
class UserDetailAPIView(APIView):
    permission_classes = [AllowAnyAccess]

    def get(self, request, unique_id):
        user = get_object_or_404(CustomUser, unique_id=unique_id)
        serializer = RegisterSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

# Update user details
class UserUpdateAPIView(APIView):
    permission_classes = [AllowAnyAccess]

    def put(self, request, unique_id):
        user = get_object_or_404(CustomUser, unique_id=unique_id)
        serializer = RegisterSerializer(user, data=request.data, partial=True)  # Allow partial updates
        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "User updated successfully",
                "user": serializer.data
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Delete a user
class UserDeleteAPIView(APIView):
    permission_classes = [AllowAnyAccess]

    def delete(self, request, unique_id):
        user = get_object_or_404(CustomUser, unique_id=unique_id)
        user.delete()
        return Response({"message": "User deleted successfully"}, status=status.HTTP_204_NO_CONTENT)

class LoginAPIView(APIView):
    permission_classes = [AllowAnyAccess]

    @extend_schema(
        request=LoginSerializer,
        summary="Login Endpoint",
        description="Authenticate a user using email and password.",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "success": {"type": "boolean"},
                    "message": {"type": "string"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "token": {"type": "string"},
                            "user_type": {"type": "string"},
                            "email": {"type": "string"}
                        }
                    }
                }
            }
        }
    )
    def post(self, request):
        try:
            logger.info(f"Login attempt received for data: {request.data}")
            
            serializer = LoginSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Serializer validation failed: {serializer.errors}")
                return Response(
                    format_validation_errors(serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST
                )

            email = serializer.validated_data['email']
            password = serializer.validated_data['password']
            
            logger.info(f"Attempting to authenticate user: {email}")
            
            # First check if user exists
            User = get_user_model()
            try:
                user = User.objects.get(email=email)
                logger.info(f"User found: {user.email}, Active status: {user.is_active}")
            except User.DoesNotExist:
                logger.error(f"No user found with email: {email}")
                return Response(
                    format_error_response("No account found with this email"),
                    status=status.HTTP_401_UNAUTHORIZED
                )

            if not user.is_active:
                logger.error(f"User {email} is not active")
                return Response(
                    format_error_response("Account is not active"),
                    status=status.HTTP_401_UNAUTHORIZED
                )

            # Try authentication
            authenticated_user = authenticate(
                request,
                username=email,
                email=email,
                password=password
            )

            if authenticated_user:
                logger.info(f"Authentication successful for user: {email}")
                token, _ = Token.objects.get_or_create(user=authenticated_user)
                
                response_data = {
                    "token": token.key,
                    "user_type": authenticated_user.user_type,
                    "user_id": authenticated_user.id,
                    "email": authenticated_user.email,
                    "first_name": authenticated_user.first_name,
                    "last_name": authenticated_user.last_name,
                    "unique_id": authenticated_user.unique_id,
                    "organization_name": authenticated_user.organization_name,
                }
                return Response(
                    format_success_response("Login successful", response_data),
                    status=status.HTTP_200_OK
                )
            else:
                logger.error(f"Password verification failed for user: {email}")
                return Response(
                    format_error_response("Invalid password"),
                    status=status.HTTP_401_UNAUTHORIZED
                )

        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return Response(
                format_error_response(f"An unexpected error occurred"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class LogoutAPIView(APIView):
    permission_classes = [AllowAnyAccess]  # Changed from IsAuthenticated
    authentication_classes = []  # Remove TokenAuthentication

    def post(self, request):
        request.auth.delete()
        return Response({"message": "Logged out successfully"}, status=status.HTTP_200_OK)

@extend_schema(
    request=NewsletterSubscriptionSerializer,
    responses={201: NewsletterSubscriptionSerializer},
    summary="Subscribe to newsletter",
    description="Subscribe to the newsletter with email and optional preferences.",
)
class NewsletterSubscriptionView(APIView):
    permission_classes = [AllowAnyAccess]
    
    def get(self, request):
        """List all newsletter subscriptions"""
        is_active = request.query_params.get('active', None)
        interests = request.query_params.get('interests', None)
        
        subscriptions = NewsletterSubscription.objects.all()
        
        # Filter by active status if specified
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            subscriptions = subscriptions.filter(is_active=is_active)
            
        # Filter by interests if specified
        if interests:
            interests_list = interests.split(',')
            for interest in interests_list:
                subscriptions = subscriptions.filter(interests__contains=[interest])
        
        serializer = NewsletterSubscriptionSerializer(subscriptions, many=True)
        return Response({
            "success": True,
            "count": len(serializer.data),
            "data": serializer.data
        })
    
    def post(self, request):
        serializer = NewsletterSubscriptionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "success": True,
                "message": "Successfully subscribed to newsletter",
                "data": serializer.data
            }, status=status.HTTP_201_CREATED)
        return Response(format_error_response(serializer.errors), status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        email = request.data.get('email')
        if not email:
            return Response({
                "success": False,
                "message": "Email is required"
            }, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            subscription = NewsletterSubscription.objects.get(email=email)
            subscription.is_active = False
            subscription.save()
            return Response({
                "success": True,
                "message": "Successfully unsubscribed from newsletter"
            })
        except NewsletterSubscription.DoesNotExist:
            return Response({
                "success": False,
                "message": "Email not found in subscription list"
            }, status=status.HTTP_404_NOT_FOUND)
