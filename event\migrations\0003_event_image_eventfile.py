# Generated by Django 4.2 on 2025-07-05 05:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('event', '0002_event_like_count_event_share_count_event_view_count'),
    ]

    operations = [
        migrations.AddField(
            model_name='event',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='event_images/'),
        ),
        migrations.CreateModel(
            name='EventFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='event_files/')),
                ('file_type', models.CharField(choices=[('image', 'Image'), ('video', 'Video'), ('pdf', 'PDF'), ('other', 'Other')], max_length=10)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='event.event')),
            ],
        ),
    ]
