from rest_framework.views import exception_handler
from rest_framework.response import Response

def format_error_response(message):
    """
    Format error messages into a consistent structure
    Args:
        message: String message to be returned to the user
    Returns:
        dict: Formatted error response
    """
    return {
        "success": False,
        "message": message
    }

def format_validation_errors(errors):
    """
    Format validation errors from serializers
    Args:
        errors: Dictionary of field errors from serializer
    Returns:
        dict: Formatted validation error response
    """
    if isinstance(errors, dict):
        # Get the first error message from each field
        messages = []
        for field, error_list in errors.items():
            if isinstance(error_list, list):
                messages.append(f"{field}: {error_list[0]}")
            else:
                messages.append(f"{field}: {str(error_list)}")
        return format_error_response(". ".join(messages))
    return format_error_response(str(errors))

def format_success_response(message, data=None):
    """
    Format success responses consistently
    Args:
        message: Success message
        data: Optional data to include in response
    Returns:
        dict: Formatted success response
    """
    response = {
        "success": True,
        "message": message
    }
    if data is not None:
        response["data"] = data
    return response


def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # If response is None, there was an unhandled exception
    if response is None:
        return None
    
    # Format the response data
    if hasattr(response, 'data'):
        response.data = format_error_response(response.data)
    
    return response