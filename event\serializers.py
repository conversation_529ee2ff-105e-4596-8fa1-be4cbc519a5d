from rest_framework import serializers
from .models import Event, TicketClass, Contestant, Poll, PollOption, Vote, Ticket, EventFile


### 🔹 Ticket Class Serializer
class TicketClassSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketClass
        fields = ['id', 'class_name', 'price', 'quantity_available', 'availability']

### 🔹 Event Serializer (With Create & Update Methods)
class EventSerializer(serializers.ModelSerializer):
    ticket_classes = TicketClassSerializer(many=True, required=False)
    popularity_score = serializers.SerializerMethodField()
    ticket_count = serializers.SerializerMethodField()
    image = serializers.ImageField(required=False, allow_null=True)
    
    class Meta:
        model = Event
        fields = [
            'id', 'title', 'location', 'start_date', 'end_date', 'category',
            'description', 'event_type', 'tags', 'ticket_classes', 'voting_details',
            'dress_code', 'parking_details', 'additional_features',
            'organizer_contact', 'social_media_links', 'created_at', 'host',
            # New fields
            'view_count', 'like_count', 'share_count', 'popularity_score', 'ticket_count',
            'image'  # Add image field
        ]
        read_only_fields = ['view_count', 'like_count', 'share_count', 'popularity_score', 'ticket_count', 'created_at', 'host']
    
    def get_popularity_score(self, obj):
        """Get the popularity score for the event"""
        return obj.popularity_score()
    
    def get_ticket_count(self, obj):
        """Get the number of tickets sold for the event"""
        return obj.tickets.count()
        
    def create(self, validated_data):
        ticket_classes_data = validated_data.pop('ticket_classes', [])
        image = validated_data.pop('image', None)
        event = Event.objects.create(**validated_data)
        if image:
            event.image = image
            event.save()
        for ticket_data in ticket_classes_data:
            TicketClass.objects.create(event=event, **ticket_data)
        return event

    def update(self, instance, validated_data):
        ticket_classes_data = validated_data.pop('ticket_classes', [])
        image = validated_data.pop('image', None)
        
        instance.title = validated_data.get('title', instance.title)
        instance.location = validated_data.get('location', instance.location)
        instance.start_date = validated_data.get('start_date', instance.start_date)
        instance.end_date = validated_data.get('end_date', instance.end_date)
        instance.category = validated_data.get('category', instance.category)
        instance.description = validated_data.get('description', instance.description)
        instance.tags = validated_data.get('tags', instance.tags)
        instance.voting_details = validated_data.get('voting_details', instance.voting_details)
        instance.dress_code = validated_data.get('dress_code', instance.dress_code)
        instance.parking_details = validated_data.get('parking_details', instance.parking_details)
        instance.additional_features = validated_data.get('additional_features', instance.additional_features)
        instance.organizer_contact = validated_data.get('organizer_contact', instance.organizer_contact)
        instance.social_media_links = validated_data.get('social_media_links', instance.social_media_links)
        
        if image is not None:
            instance.image = image
        instance.save()

        # Handle ticket class updates
        existing_ticket_classes = {tc.id: tc for tc in instance.ticket_classes.all()}
        for ticket_data in ticket_classes_data:
            ticket_id = ticket_data.get('id', None)
            if ticket_id and ticket_id in existing_ticket_classes:
                ticket_obj = existing_ticket_classes.pop(ticket_id)
                for key, value in ticket_data.items():
                    setattr(ticket_obj, key, value)
                ticket_obj.save()
            else:
                TicketClass.objects.create(event=instance, **ticket_data)

        # Delete ticket classes that were removed
        for remaining_ticket in existing_ticket_classes.values():
            remaining_ticket.delete()

        return instance


class ContestantSerializer(serializers.ModelSerializer):
    """Serializer for Contestants in a Contest"""

    class Meta:
        model = Contestant
        fields = ['id', 'name', 'age', 'image', 'description']
        
        
### 🔹 Ticket Serializer
class TicketSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ticket
        fields = '__all__'
        
        
### 🔹 Poll Option Serializer
class PollOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PollOption
        fields = ['id', 'poll', 'option_text']

### 🔹 Poll Serializer (With Create & Update Methods)
class PollSerializer(serializers.ModelSerializer):
    options = PollOptionSerializer(many=True)

    class Meta:
        model = Poll
        fields = ['id', 'event', 'poll_question', 'multiple_votes_allowed', 'cost_per_vote', 'options', 'created_at']

    def create(self, validated_data):
        options_data = validated_data.pop('options', [])
        poll = Poll.objects.create(**validated_data)

        for option_data in options_data:
            PollOption.objects.create(poll=poll, **option_data)

        return poll

    def update(self, instance, validated_data):
        options_data = validated_data.pop('options', [])

        instance.poll_question = validated_data.get('poll_question', instance.poll_question)
        instance.multiple_votes_allowed = validated_data.get('multiple_votes_allowed', instance.multiple_votes_allowed)
        instance.cost_per_vote = validated_data.get('cost_per_vote', instance.cost_per_vote)
        instance.save()

        # Handle poll option updates
        existing_options = {option.id: option for option in instance.options.all()}
        for option_data in options_data:
            option_id = option_data.get('id', None)
            if option_id and option_id in existing_options:
                option_obj = existing_options.pop(option_id)
                for key, value in option_data.items():
                    setattr(option_obj, key, value)
                option_obj.save()
            else:
                PollOption.objects.create(poll=instance, **option_data)

        # Delete options that were removed
        for remaining_option in existing_options.values():
            remaining_option.delete()

        return instance

### 🔹 Vote Serializer (For Voting API)
class VoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vote
        fields = ['id', 'poll_option', 'user', 'transaction_id', 'amount', 'created_at']


class PopularEventSerializer(serializers.ModelSerializer):
    ticket_count = serializers.SerializerMethodField()
    popularity_score = serializers.SerializerMethodField()
    
    class Meta:
        model = Event
        fields = [
            'id', 'title', 'location', 'start_date', 'end_date', 'category',
            'description', 'event_type', 'view_count', 'like_count', 'share_count',
            'ticket_count', 'popularity_score'
        ]
    
    def get_ticket_count(self, obj):
        """Get the number of tickets sold for the event"""
        return obj.tickets.count()
    
    def get_popularity_score(self, obj):
        """Get the popularity score for the event"""
        return obj.popularity_score()


class EventFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventFile
        fields = ['id', 'event', 'file', 'file_type', 'uploaded_at']
        read_only_fields = ['id', 'uploaded_at']





