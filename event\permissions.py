from rest_framework import permissions

class IsEventHostOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow hosts of an event to edit it.
    """
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request
        if request.method in permissions.SAFE_METHODS:
            return True
            
        # Write permissions are only allowed to the host of the event
        if hasattr(obj, 'host'):
            return obj.host == request.user
        elif hasattr(obj, 'event') and hasattr(obj.event, 'host'):
            return obj.event.host == request.user
        return False
