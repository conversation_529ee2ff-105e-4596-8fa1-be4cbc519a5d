from django.db import models
from django.contrib.auth import get_user_model
from account.models import CustomUser
from django.utils.timezone import now
from django.utils import timezone
import datetime
import json
from cloudinary.models import CloudinaryField

User = get_user_model()

def get_future_datetime():
    """Return a datetime 2 hours in the future"""
    return timezone.now() + timezone.timedelta(hours=2)

def get_future_date_30days():
    """Return a datetime 30 days in the future"""
    return timezone.now() + timezone.timedelta(days=30)

def default_list():
    """Return an empty list for JSONField default"""
    return []

def default_dict():
    """Return an empty dict for JSONField default"""
    return {}

class EventManager(models.Manager):
    def get_popular(self, limit=10):
        """
        Get popular events based on ticket sales, views, likes, and shares
        """
        # Get all events
        events = self.get_queryset()
        
        # Annotate with ticket count
        from django.db.models import Count
        events = events.annotate(ticket_count=Count('tickets'))
        
        # Order by our popularity factors
        events = events.order_by(
            '-ticket_count',  # Most tickets first
            '-view_count',    # Then most views
            '-like_count',    # Then most likes
            '-share_count',   # Then most shares
            '-created_at'     # Then newest
        )
        
        # Limit results
        return events[:limit]
    
    def get_popular_by_month(self, year=None, month=None, limit=10):
        """
        Get popular events for a specific month
        """
        from django.utils import timezone
        import datetime
        
        # Default to current month if not specified
        if year is None or month is None:
            now = timezone.now()
            year = now.year
            month = now.month
            
        # Create date range for the month
        start_date = datetime.date(year, month, 1)
        if month == 12:
            end_date = datetime.date(year + 1, 1, 1) - datetime.timedelta(days=1)
        else:
            end_date = datetime.date(year, month + 1, 1) - datetime.timedelta(days=1)
            
        # Filter events in this month
        events = self.get_queryset().filter(
            start_date__gte=start_date,
            start_date__lte=end_date
        )
        
        # Annotate with ticket count
        from django.db.models import Count
        events = events.annotate(ticket_count=Count('tickets'))
        
        # Order by our popularity factors
        events = events.order_by(
            '-ticket_count',
            '-view_count',
            '-like_count',
            '-share_count'
        )
        
        # Limit results
        return events[:limit]
    

class Event(models.Model):
    CATEGORY_CHOICES = [
        ('Pageantry', 'Pageantry'),
        ('Music Festival', 'Music Festival'),
        ('Conference', 'Conference'),
        ('Workshop', 'Workshop'),
        ('Sports', 'Sports'),
        ('Other', 'Other'),
    ]
    
    EVENT_TYPE_CHOICES = [
        ('event', 'Event'),
        ('contest', 'Contest'),
        ('form', 'Form'),
    ]
    
    host = models.ForeignKey('account.CustomUser', on_delete=models.CASCADE, related_name='hosted_events', null=True, blank=True)
    title = models.CharField(max_length=255, default="Untitled Event")
    location = models.CharField(max_length=255, default="TBD")
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(default=get_future_datetime)
    category = models.CharField(max_length=100, choices=CATEGORY_CHOICES, default='Other')
    description = models.TextField(default="No description provided")
    
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES, default='event')
    
    tags = models.JSONField(default=default_list, blank=True)
    voting_details = models.JSONField(default=default_dict, blank=True)
    dress_code = models.CharField(max_length=255, blank=True, default="")
    parking_details = models.TextField(blank=True, default="")
    additional_features = models.JSONField(default=default_dict, blank=True)
    organizer_contact = models.JSONField(default=default_dict, blank=True)
    social_media_links = models.JSONField(default=default_dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    image = CloudinaryField('image', null=True, blank=True)
    
    # New fields for tracking popularity
    view_count = models.PositiveIntegerField(default=0)
    like_count = models.PositiveIntegerField(default=0)
    share_count = models.PositiveIntegerField(default=0)
    
    # Method to calculate popularity score
    def popularity_score(self):
        """Calculate a popularity score based on multiple factors"""
        # Get number of tickets sold
        ticket_count = self.tickets.count()
        
        # Calculate weighted score (you can adjust weights as needed)
        score = (
            (ticket_count * 5) +  # Tickets have highest weight
            (self.view_count * 1) +  # Views have medium weight
            (self.like_count * 2) +  # Likes have medium-high weight
            (self.share_count * 3)   # Shares have high weight
        )
        return score
    
    # Method to increment view count
    def increment_view(self):
        """Increment the view count for this event"""
        self.view_count += 1
        self.save(update_fields=['view_count'])
        
    # Method to increment like count
    def increment_like(self):
        """Increment the like count for this event"""
        self.like_count += 1
        self.save(update_fields=['like_count'])
        
    # Method to increment share count
    def increment_share(self):
        """Increment the share count for this event"""
        self.share_count += 1
        self.save(update_fields=['share_count'])
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        # Ensure JSON fields are properly formatted before saving
        if isinstance(self.tags, str):
            try:
                self.tags = json.loads(self.tags)
            except json.JSONDecodeError:
                self.tags = []
                
        if isinstance(self.voting_details, str):
            try:
                self.voting_details = json.loads(self.voting_details)
            except json.JSONDecodeError:
                self.voting_details = {}
                
        if isinstance(self.additional_features, str):
            try:
                self.additional_features = json.loads(self.additional_features)
            except json.JSONDecodeError:
                self.additional_features = {}
                
        if isinstance(self.organizer_contact, str):
            try:
                self.organizer_contact = json.loads(self.organizer_contact)
            except json.JSONDecodeError:
                self.organizer_contact = {}
                
        if isinstance(self.social_media_links, str):
            try:
                self.social_media_links = json.loads(self.social_media_links)
            except json.JSONDecodeError:
                self.social_media_links = {}
                
        super().save(*args, **kwargs)

    # Add the custom manager
    objects = EventManager()


class TicketClass(models.Model):
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='ticket_classes')
    class_name = models.CharField(max_length=100, default="Standard")
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_available = models.PositiveIntegerField(default=100)
    availability = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.class_name} - {self.event.title}"


class Ticket(models.Model):
    TICKET_TYPE_CHOICES = [
        ('paid', 'Paid'),
        ('free', 'Free')
    ]
    
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='tickets')
    ticket_type = models.CharField(max_length=10, choices=TICKET_TYPE_CHOICES, default='free')
    refunds_available = models.BooleanField(default=False)
    quantity = models.PositiveIntegerField(default=1)
    sales_start = models.DateTimeField(default=timezone.now)
    sales_end = models.DateTimeField(default=get_future_date_30days)
    
    def __str__(self):
        return f"{self.event.title} - {self.ticket_type}"    

class Contestant(models.Model):
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='contestants')
    image = models.ImageField(upload_to='contestants/', null=True, blank=True)
    name = models.CharField(max_length=100, default="Anonymous Contestant")
    age = models.PositiveIntegerField(default=18)
    description = models.TextField(default="No description provided")
    
    def __str__(self):
        return self.name  


class Poll(models.Model):
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name="polls")
    poll_question = models.CharField(max_length=500, default="Vote for your favorite")
    multiple_votes_allowed = models.BooleanField(default=False)
    cost_per_vote = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, default=0.00)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Poll for {self.event.title}: {self.poll_question}"

class PollOption(models.Model):
    poll = models.ForeignKey(Poll, on_delete=models.CASCADE, related_name="options")
    option_text = models.CharField(max_length=255, default="Option")

    def __str__(self):
        return f"{self.option_text} (Poll: {self.poll.poll_question})"



class Vote(models.Model):
    poll_option = models.ForeignKey(PollOption, on_delete=models.CASCADE, related_name="votes", null=True, blank=True)
    user = models.ForeignKey('account.CustomUser', on_delete=models.CASCADE, related_name="votes", null=True, blank=True)
    transaction_id = models.CharField(max_length=255, blank=True, default="")
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_str = self.user.username if self.user else "Anonymous"
        option_str = self.poll_option.option_text if self.poll_option else "No option"
        return f"Vote by {user_str} for {option_str}"

class EventFile(models.Model):
    EVENT_FILE_TYPES = [
        ('image', 'Image'),
        ('video', 'Video'),
        ('pdf', 'PDF'),
        ('other', 'Other'),
    ]
    event = models.ForeignKey(Event, related_name='files', on_delete=models.CASCADE)
    file = models.FileField(upload_to='event_files/')
    file_type = models.CharField(max_length=10, choices=EVENT_FILE_TYPES)
    uploaded_at = models.DateTimeField(auto_now_add=True)



