{"name": "Deebo Backend API", "description": "A Django-based REST API for event management, ticketing, and voting systems", "repository": "https://github.com/DeeboInc/deebo-backend-api", "keywords": ["python", "django", "api", "events"], "env": {"SECRET_KEY": {"description": "A secret key for Django", "generator": "secret"}, "DEBUG": {"description": "Debug mode", "value": "False"}}, "addons": ["heroku-postgresql"], "buildpacks": [{"url": "heroku/python"}]}