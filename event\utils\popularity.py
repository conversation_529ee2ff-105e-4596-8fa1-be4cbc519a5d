from django.db.models import Count, F, ExpressionWrapper, FloatField
from django.utils import timezone
import datetime

def calculate_trending_score(event):
    """
    Calculate a trending score based on recent activity
    Higher weight for recent activity
    """
    # Get current time
    now = timezone.now()
    
    # Get recent tickets (last 7 days)
    recent_tickets = event.tickets.filter(
        created_at__gte=now - datetime.timedelta(days=7)
    ).count()
    
    # Calculate recency factor (higher for newer events)
    days_active = max(1, (now - event.created_at).days)
    recency_factor = 1.0 / days_active
    
    # Calculate trending score
    trending_score = (
        (event.view_count * 0.1) +
        (event.like_count * 0.5) +
        (event.share_count * 1.0) +
        (recent_tickets * 2.0)
    ) * recency_factor * 100
    
    return trending_score

def get_trending_events(limit=10):
    """
    Get trending events based on recent activity
    """
    from event.models import Event
    
    # Get all events
    events = Event.objects.all()
    
    # Calculate trending score for each event
    events_with_scores = [(event, calculate_trending_score(event)) for event in events]
    
    # Sort by trending score
    events_with_scores.sort(key=lambda x: x[1], reverse=True)
    
    # Return top events
    return [event for event, score in events_with_scores[:limit]]