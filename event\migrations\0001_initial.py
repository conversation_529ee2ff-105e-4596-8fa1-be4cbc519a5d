# Generated by Django 4.2 on 2025-06-06 23:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import event.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='Untitled Event', max_length=255)),
                ('location', models.CharField(default='TBD', max_length=255)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(default=event.models.get_future_datetime)),
                ('category', models.CharField(choices=[('Pageantry', 'Pageantry'), ('Music Festival', 'Music Festival'), ('Conference', 'Conference'), ('Workshop', 'Workshop'), ('Sports', 'Sports'), ('Other', 'Other')], default='Other', max_length=100)),
                ('description', models.TextField(default='No description provided')),
                ('event_type', models.CharField(choices=[('event', 'Event'), ('contest', 'Contest'), ('form', 'Form')], default='event', max_length=20)),
                ('tags', models.JSONField(blank=True, default=event.models.default_list)),
                ('voting_details', models.JSONField(blank=True, default=event.models.default_dict)),
                ('dress_code', models.CharField(blank=True, default='', max_length=255)),
                ('parking_details', models.TextField(blank=True, default='')),
                ('additional_features', models.JSONField(blank=True, default=event.models.default_dict)),
                ('organizer_contact', models.JSONField(blank=True, default=event.models.default_dict)),
                ('social_media_links', models.JSONField(blank=True, default=event.models.default_dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('host', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hosted_events', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Poll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('poll_question', models.CharField(default='Vote for your favorite', max_length=500)),
                ('multiple_votes_allowed', models.BooleanField(default=False)),
                ('cost_per_vote', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='polls', to='event.event')),
            ],
        ),
        migrations.CreateModel(
            name='PollOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option_text', models.CharField(default='Option', max_length=255)),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='event.poll')),
            ],
        ),
        migrations.CreateModel(
            name='Vote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(blank=True, default='', max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('poll_option', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='event.polloption')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='votes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TicketClass',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('class_name', models.CharField(default='Standard', max_length=100)),
                ('price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_available', models.PositiveIntegerField(default=100)),
                ('availability', models.BooleanField(default=True)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticket_classes', to='event.event')),
            ],
        ),
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_type', models.CharField(choices=[('paid', 'Paid'), ('free', 'Free')], default='free', max_length=10)),
                ('refunds_available', models.BooleanField(default=False)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('sales_start', models.DateTimeField(default=django.utils.timezone.now)),
                ('sales_end', models.DateTimeField(default=event.models.get_future_date_30days)),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tickets', to='event.event')),
            ],
        ),
        migrations.CreateModel(
            name='Contestant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='contestants/')),
                ('name', models.CharField(default='Anonymous Contestant', max_length=100)),
                ('age', models.PositiveIntegerField(default=18)),
                ('description', models.TextField(default='No description provided')),
                ('event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contestants', to='event.event')),
            ],
        ),
    ]
