from django.urls import path
from .views import ( 
    RegisterAPIView, 
    LoginAPIView, 
    LogoutAPIView,  
    UserListAPIView,
    UserDetailAPIView,
    UserUpdateAPIView,
    UserDeleteAPIView,
    NewsletterSubscriptionView
)

urlpatterns = [
    path('register/', RegisterAPIView.as_view(), name='register'),
    path('login/', LoginAPIView.as_view(), name='login'),
    path('logout/', LogoutAPIView.as_view(), name='logout'),
    path('users/', UserListAPIView.as_view(), name='user-list'),
    path('users/<str:unique_id>/', UserDetailAPIView.as_view(), name='user-detail'),
    path('users/<str:unique_id>/update/', UserUpdateAPIView.as_view(), name='user-update'),
    path('users/<str:unique_id>/delete/', UserDeleteAPIView.as_view(), name='user-delete'),
    
    # Newsletter subscription endpoints
    path('newsletter/', NewsletterSubscriptionView.as_view(), name='newsletter-subscription'),
]
